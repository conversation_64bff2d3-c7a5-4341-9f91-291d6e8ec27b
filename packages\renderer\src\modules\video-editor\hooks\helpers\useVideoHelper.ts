import { useCallback } from 'react'
import { OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { toast } from 'react-toastify'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import {
  byStartFrame,
  byStoryboard,
  findOverlayStoryboard,
  findTrackByOverlay,
  getOverlayTimeRange
} from '@/modules/video-editor/utils/overlay-helper'

type VideoHelper = {
  /**
   * 翻转指定的视频 Overlay
   * @param overlay 需要翻转的视频 Overlay
   * @param copyOnly 为 `true` 时表示镜像后复制到剪贴板, 不会修改原 Overlay; 否则直接修改原 Overlay. 默认值: `false`
   */
  flipCurrentOverlay(overlay: VideoOverlay, copyOnly?: boolean): void

  /**
   * 变速视频以自动对齐到分镜时长
   */
  autoAdjustVideoSpeed(overlay: VideoOverlay): void
}

export const useVideoHelper = (): VideoHelper => {
  const {
    tracks,
    updateOverlay,
    updateTracks,
  } = useEditorContext()

  const { clipboard } = useTimeline()

  const flipCurrentOverlay = useCallback(
    (overlay: VideoOverlay, copyOnly = false) => {
      if (overlay.type !== OverlayType.VIDEO) return

      const transformWhenFlipped = 'scaleX(-1)'

      const updated: VideoOverlay = {
        ...overlay,
        styles: {
          ...overlay.styles,
          transform: overlay.styles?.transform === transformWhenFlipped
            ? ''
            : transformWhenFlipped
        }
      }

      if (copyOnly) {
        clipboard.copyOverlay(updated)
        toast('已复制镜像后的视频')
        return
      }

      return updateOverlay(overlay.id, () => updated)
    },
    [clipboard]
  )

  const autoAdjustVideoSpeed = useCallback((overlay: VideoOverlay) => {
    if (overlay.type !== OverlayType.VIDEO) return

    const videoOverlay = overlay as VideoOverlay
    const storyboard = findOverlayStoryboard(tracks, videoOverlay)
    if (!storyboard) {
      toast.error('无法找到视频所在的分镜')
      return
    }

    // 检查必要的属性
    if (!videoOverlay.originalDurationInFrames || videoOverlay.originalDurationInFrames <= 0) {
      toast.error('视频原始时长信息缺失，无法进行自动变速')
      return
    }

    const storyboardDuration = storyboard.durationInFrames
    if (storyboardDuration <= 0) {
      toast.error('分镜时长无效，无法进行自动变速')
      return
    }

    return updateTracks(prevTracks => {
      // 找到目标视频所在的轨道
      const targetTrack = findTrackByOverlay(prevTracks, videoOverlay.id)
      if (!targetTrack) return prevTracks

      // 查找同一分镜和轨道下的所有其他视频 overlay
      const sameTrackVideos = targetTrack.overlays
        .filter(byStoryboard(storyboard))
        .filter(o => o.type === OverlayType.VIDEO && o.id !== videoOverlay.id)
        .sort(byStartFrame())

      // 计算其他视频的总时长
      const otherVideosTotalDuration = sameTrackVideos.reduce((total, video) => {
        return total + video.durationInFrames
      }, 0)

      // 计算目标视频需要的新时长
      const targetNewDuration = storyboardDuration - otherVideosTotalDuration

      if (targetNewDuration <= 0) {
        toast.error('分镜剩余时长不足，无法调整视频速度')
        return prevTracks
      }

      // 计算新的播放速度
      const newSpeed = videoOverlay.originalDurationInFrames / targetNewDuration

      const finalDuration = videoOverlay.originalDurationInFrames / newSpeed

      // 创建更新后的轨道数组
      const updatedTracks = [...prevTracks]
      const updatedTrack = { ...targetTrack }
      const updatedOverlays = [...updatedTrack.overlays]

      // 找到目标视频在轨道中的索引
      const videoIndex = updatedOverlays.findIndex(o => o.id === videoOverlay.id)
      if (videoIndex === -1) return prevTracks

      // 更新目标视频的属性
      const updatedVideo: VideoOverlay = {
        ...videoOverlay,
        speed: newSpeed,
        durationInFrames: Math.round(finalDuration)
      }

      updatedOverlays[videoIndex] = updatedVideo

      // 计算时长变化量
      const durationChange = updatedVideo.durationInFrames - videoOverlay.durationInFrames

      // 如果时长发生变化，需要调整后续视频的位置
      if (durationChange !== 0) {
        const [, videoEndFrame] = getOverlayTimeRange(videoOverlay)

        // 找到需要调整位置的后续视频（在同一分镜内且开始时间在当前视频结束时间之后）
        const videosToAdjust = updatedOverlays
          .filter(byStoryboard(storyboard))
          .filter(o => o.from >= videoEndFrame)
          .sort(byStartFrame())

        // 调整后续视频的开始时间
        videosToAdjust.forEach(video => {
          const videoIndexToUpdate = updatedOverlays.findIndex(o => o.id === video.id)
          if (videoIndexToUpdate !== -1) {
            updatedOverlays[videoIndexToUpdate] = {
              ...video,
              from: video.from + durationChange
            }
          }
        })
      }

      // 更新轨道
      updatedTrack.overlays = updatedOverlays
      updatedTracks[targetTrack.index] = updatedTrack

      return updatedTracks
    })
  }, [tracks, updateTracks])

  return {
    flipCurrentOverlay,
    autoAdjustVideoSpeed
  }
}

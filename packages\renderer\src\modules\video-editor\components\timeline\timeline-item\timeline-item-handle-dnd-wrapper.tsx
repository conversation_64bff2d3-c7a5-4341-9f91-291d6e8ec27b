import { DndContext, DragMoveEvent, DragStartEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import React, { PropsWithChildren, useCallback } from 'react'
import { useTimeline } from '@/modules/video-editor/contexts'

export const TimelineItemHandleDndWrapper: React.FC<PropsWithChildren> = ({ children }) => {
  const {
    handleOverlayDragStart, handleOverlayDragMove, handleOverlayDragEnd
  } = useTimeline()

  const sensors = useSensors(useSensor(PointerSensor, {
    activationConstraint: {
      distance: 4,
    }
  }))

  const onDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event

    if (active.data.current?.type !== 'timeline-item-handle') return

    const { overlay } = active.data.current as any
    return handleOverlayDragStart(overlay, 'resize-end')
  }, [handleOverlayDragStart])

  const onDragMove = useCallback((event: DragMoveEvent) => {
    const { x: deltaX, y: deltaY } = event.delta
    return handleOverlayDragMove(deltaX, deltaY)
  }, [handleOverlayDragMove])

  return (
    <DndContext sensors={sensors} onDragStart={onDragStart} onDragMove={onDragMove} onDragEnd={handleOverlayDragEnd}>
      {children}
    </DndContext>
  )
}
